<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Library Management System{% endblock %}</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'ui-sans-serif', 'system-ui'],
                    },
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        },
                        secondary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-in': 'slideIn 0.3s ease-out',
                        'bounce-subtle': 'bounceSubtle 2s infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideIn: {
                            '0%': { transform: 'translateY(-10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        },
                        bounceSubtle: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-5px)' },
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }

        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .btn-primary {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
            transform: translateY(-1px);
            box-shadow: 0 10px 15px -3px rgba(59, 130, 246, 0.4);
        }

        .sidebar-item {
            transition: all 0.2s ease;
        }

        .sidebar-item:hover {
            background: rgba(59, 130, 246, 0.1);
            border-left: 4px solid #3b82f6;
        }

        .notification-badge {
            animation: pulse 2s infinite;
        }

        /* Form field styling to ensure visibility */
        .form-control,
        input[type="text"],
        input[type="email"],
        input[type="password"],
        input[type="number"],
        input[type="date"],
        input[type="tel"],
        textarea,
        select {
            background-color: #ffffff !important;
            border: 2px solid #d1d5db !important;
            border-radius: 8px !important;
            padding: 12px 16px !important;
            font-size: 14px !important;
            line-height: 1.5 !important;
            color: #374151 !important;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
            transition: all 0.2s ease-in-out !important;
        }

        .form-control:focus,
        input[type="text"]:focus,
        input[type="email"]:focus,
        input[type="password"]:focus,
        input[type="number"]:focus,
        input[type="date"]:focus,
        input[type="tel"]:focus,
        textarea:focus,
        select:focus {
            outline: none !important;
            border-color: #3b82f6 !important;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
        }

        .form-control::placeholder,
        input::placeholder,
        textarea::placeholder {
            color: #9ca3af !important;
        }

        /* Ensure form labels are visible */
        label {
            color: #374151 !important;
            font-weight: 500 !important;
            margin-bottom: 8px !important;
            display: block !important;
        }
    </style>

    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/library.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen font-sans">
    <!-- Navigation -->
    <nav class="gradient-bg shadow-xl relative z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-20">
                <div class="flex items-center">
                    <a href="{% url 'home' %}" class="flex items-center space-x-3 group">
                        <div class="relative">
                            <i class="fas fa-book-open text-3xl text-white group-hover:text-blue-200 transition-colors duration-300"></i>
                            <div class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-2xl font-bold text-white group-hover:text-blue-100 transition-colors duration-300">LibraryPro</span>
                            <span class="text-xs text-blue-200 font-medium">Management System</span>
                        </div>
                    </a>
                </div>

                {% if user.is_authenticated %}
                <div class="flex items-center space-x-4">
                    <!-- Search Bar -->
                    <div class="flex-1 max-w-lg">
                        <form action="{% url 'advanced_search' %}" method="get" class="relative">
                            <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none z-10" style="padding-left: 1rem;">
                                <i class="fas fa-search text-white/70 text-sm"></i>
                            </div>
                            <input type="text"
                                   name="query"
                                   class="block w-full pr-3 py-2 border border-white/20 rounded-lg bg-white/10 text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-white/50 backdrop-blur-sm"
                                   placeholder="Search books, users, loans..." style="padding-left: 3rem;"
                                   id="global-search">
                        </form>
                    </div>

                    <!-- Notifications -->
                    <div class="relative group">
                        <button class="p-2 text-white/80 hover:text-white hover:bg-white/20 rounded-lg transition-all duration-200 relative">
                            <i class="fas fa-bell text-lg"></i>
                            <span id="notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center hidden">0</span>
                        </button>

                        <!-- Notification Dropdown -->
                        <div class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg py-1 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 border border-gray-200">
                            <div class="px-4 py-3 border-b border-gray-200">
                                <h3 class="text-sm font-semibold text-gray-900">Notifications</h3>
                            </div>
                            <div id="notification-list" class="max-h-64 overflow-y-auto">
                                <div class="px-4 py-3 text-sm text-gray-500 text-center">
                                    No new notifications
                                </div>
                            </div>
                            <div class="px-4 py-2 border-t border-gray-200">
                                <a href="#" class="text-xs text-blue-600 hover:text-blue-800">View all notifications</a>
                            </div>
                        </div>
                    </div>

                    <!-- User Menu -->
                    <div class="relative group">
                        <button class="flex items-center space-x-3 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-xl transition-all duration-300 group">
                            <div class="w-8 h-8 bg-white/30 rounded-full flex items-center justify-center">
                                {% if user.profile_picture %}
                                <img src="{{ user.profile_picture.url }}" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                                {% else %}
                                <i class="fas fa-user text-white"></i>
                                {% endif %}
                            </div>
                            <div class="hidden sm:block text-left">
                                <div class="text-white font-medium text-sm">{{ user.get_full_name|default:user.username }}</div>
                                <div class="text-blue-200 text-xs">{{ user.get_role_display }}</div>
                            </div>
                            <i class="fas fa-chevron-down text-white text-sm group-hover:rotate-180 transition-transform duration-300"></i>
                        </button>

                        <!-- Dropdown Menu -->
                        <div class="absolute right-0 mt-3 w-64 bg-white rounded-2xl shadow-2xl py-2 z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform group-hover:translate-y-0 translate-y-2">
                            <div class="px-4 py-3 border-b border-gray-100">
                                <div class="font-semibold text-gray-800">{{ user.get_full_name|default:user.username }}</div>
                                <div class="text-sm text-gray-500">{{ user.email }}</div>
                                <div class="text-xs text-blue-600 font-medium mt-1">{{ user.get_role_display }}</div>
                            </div>
                            <a href="{% url 'profile' %}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors duration-200">
                                <i class="fas fa-user-circle mr-3 text-blue-500"></i>
                                <span>My Profile</span>
                            </a>
                            {% if user.can_manage_users %}
                            <a href="{% url 'register' %}" class="flex items-center px-4 py-3 text-gray-700 hover:bg-blue-50 transition-colors duration-200">
                                <i class="fas fa-user-plus mr-3 text-green-500"></i>
                                <span>Add User</span>
                            </a>
                            {% endif %}
                            <div class="border-t border-gray-100 my-1"></div>
                            <a href="{% url 'logout' %}" class="flex items-center px-4 py-3 text-red-600 hover:bg-red-50 transition-colors duration-200">
                                <i class="fas fa-sign-out-alt mr-3"></i>
                                <span>Sign Out</span>
                            </a>
                        </div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button class="lg:hidden text-white p-2" onclick="toggleMobileMenu()">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Mobile Menu -->
        {% if user.is_authenticated %}
        <div id="mobileMenu" class="lg:hidden hidden bg-white/10 backdrop-blur-md">
            <div class="px-4 py-3 space-y-2">
                <a href="{% url 'home' %}" class="block px-4 py-2 text-white hover:bg-white/20 rounded-lg transition-colors duration-200">
                    <i class="fas fa-home mr-3"></i>Dashboard
                </a>
                <a href="{% url 'book_list' %}" class="block px-4 py-2 text-white hover:bg-white/20 rounded-lg transition-colors duration-200">
                    <i class="fas fa-books mr-3"></i>Books
                </a>
                <a href="{% url 'advanced_search' %}" class="block px-4 py-2 text-white hover:bg-white/20 rounded-lg transition-colors duration-200">
                    <i class="fas fa-search mr-3"></i>Search
                </a>
                {% if user.can_manage_books %}
                <a href="{% url 'loan_list' %}" class="block px-4 py-2 text-white hover:bg-white/20 rounded-lg transition-colors duration-200">
                    <i class="fas fa-hand-holding mr-3"></i>Loans
                </a>
                <a href="{% url 'book_add' %}" class="block px-4 py-2 text-white hover:bg-white/20 rounded-lg transition-colors duration-200">
                    <i class="fas fa-plus mr-3"></i>Add Book
                </a>
                {% endif %}
                {% if user.can_manage_users %}
                <a href="{% url 'user_list' %}" class="block px-4 py-2 text-white hover:bg-white/20 rounded-lg transition-colors duration-200">
                    <i class="fas fa-users mr-3"></i>Users
                </a>
                {% endif %}
                {% if user.can_manage_books %}
                <a href="{% url 'reports_dashboard' %}" class="block px-4 py-2 text-white hover:bg-white/20 rounded-lg transition-colors duration-200">
                    <i class="fas fa-chart-bar mr-3"></i>Reports
                </a>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </nav>

    <!-- Messages -->
    {% if messages %}
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6">
        {% for message in messages %}
        <div class="mb-4 animate-slide-in">
            <div class="p-4 rounded-2xl shadow-lg {% if message.tags == 'error' %}bg-gradient-to-r from-red-50 to-red-100 border-l-4 border-red-500{% elif message.tags == 'warning' %}bg-gradient-to-r from-yellow-50 to-yellow-100 border-l-4 border-yellow-500{% elif message.tags == 'success' %}bg-gradient-to-r from-green-50 to-green-100 border-l-4 border-green-500{% else %}bg-gradient-to-r from-blue-50 to-blue-100 border-l-4 border-blue-500{% endif %}">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center {% if message.tags == 'error' %}bg-red-500{% elif message.tags == 'warning' %}bg-yellow-500{% elif message.tags == 'success' %}bg-green-500{% else %}bg-blue-500{% endif %}">
                            {% if message.tags == 'error' %}
                            <i class="fas fa-exclamation-circle text-white text-sm"></i>
                            {% elif message.tags == 'warning' %}
                            <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            {% elif message.tags == 'success' %}
                            <i class="fas fa-check-circle text-white text-sm"></i>
                            {% else %}
                            <i class="fas fa-info-circle text-white text-sm"></i>
                            {% endif %}
                        </div>
                    </div>
                    <div class="ml-4 flex-1">
                        <div class="{% if message.tags == 'error' %}text-red-800{% elif message.tags == 'warning' %}text-yellow-800{% elif message.tags == 'success' %}text-green-800{% else %}text-blue-800{% endif %} font-medium">
                            {{ message }}
                        </div>
                    </div>
                    <button onclick="this.parentElement.parentElement.parentElement.remove()" class="ml-4 {% if message.tags == 'error' %}text-red-500 hover:text-red-700{% elif message.tags == 'warning' %}text-yellow-500 hover:text-yellow-700{% elif message.tags == 'success' %}text-green-500 hover:text-green-700{% else %}text-blue-500 hover:text-blue-700{% endif %} transition-colors duration-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Main Layout with Sidebar -->
    {% if user.is_authenticated %}
    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="hidden lg:flex lg:flex-shrink-0">
            <div class="flex flex-col w-64">
                <div class="flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto">
                    <div class="flex items-center flex-shrink-0 px-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                                <i class="fas fa-book-open text-white text-sm"></i>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">LibraryPro</h2>
                                <p class="text-xs text-gray-500">Management System</p>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation -->
                    <nav class="mt-8 flex-1 px-2 space-y-1">
                        <!-- Dashboard -->
                        <a href="{% url 'home' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'home' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                            <i class="fas fa-home mr-3 text-lg"></i>
                            Dashboard
                        </a>

                        {% if user.can_borrow_books %}
                        <!-- Student Dashboard -->
                        <a href="{% url 'student_dashboard' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'student_dashboard' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                            <i class="fas fa-user-graduate mr-3 text-lg"></i>
                            My Library
                        </a>

                        <!-- Student Quick Links Section -->
                        <div class="mt-6">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">My Account</h3>
                            <div class="mt-2 space-y-1">
                                <!-- My Profile -->
                                <a href="{% url 'profile' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'profile' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-user mr-3 text-lg"></i>
                                    My Profile
                                </a>

                                <!-- My Loans -->
                                <a href="{% url 'my_loans' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'my_loans' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-book-reader mr-3 text-lg"></i>
                                    My Loans
                                </a>

                                <!-- My Reservations -->
                                <a href="{% url 'my_reservations' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'my_reservations' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-bookmark mr-3 text-lg"></i>
                                    My Reservations
                                </a>

                                <!-- Pending Requests -->
                                <a href="{% url 'my_pending_requests' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'my_pending_requests' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-clock mr-3 text-lg"></i>
                                    Pending Requests
                                </a>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Books -->
                        <a href="{% url 'book_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'book' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                            <i class="fas fa-books mr-3 text-lg"></i>
                            Books
                        </a>

                        {% if user.can_manage_books %}
                        <!-- Loan Management Section -->
                        <div class="mt-6">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Loan Management</h3>
                            <div class="mt-2 space-y-1">
                                <!-- Loans -->
                                <a href="{% url 'loan_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'loan' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-hand-holding mr-3 text-lg"></i>
                                    All Loans
                                </a>

                                <!-- Pending Requests -->
                                <a href="{% url 'pending_requests' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'pending_requests' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-clock mr-3 text-lg"></i>
                                    Pending Requests
                                </a>

                                <!-- Issue Loan -->
                                <a href="{% url 'loan_create' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if request.resolver_match.url_name == 'loan_create' %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-plus-circle mr-3 text-lg"></i>
                                    Issue Loan
                                </a>
                            </div>
                        </div>

                        <!-- Library Management Section -->
                        <div class="mt-6">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Library Management</h3>
                            <div class="mt-2 space-y-1">
                                <!-- Authors -->
                                <a href="{% url 'author_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'author' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-user-edit mr-3 text-lg"></i>
                                    Authors
                                </a>

                                <!-- Categories -->
                                <a href="{% url 'category_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'category' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-tags mr-3 text-lg"></i>
                                    Categories
                                </a>

                                <!-- Publishers -->
                                <a href="{% url 'publisher_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'publisher' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-building mr-3 text-lg"></i>
                                    Publishers
                                </a>

                                <!-- Sections -->
                                <a href="{% url 'section_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'section' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-layer-group mr-3 text-lg"></i>
                                    Sections
                                </a>

                                <!-- Shelf Locations -->
                                <a href="{% url 'shelf_location_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'shelf' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-map-marker-alt mr-3 text-lg"></i>
                                    Shelf Locations
                                </a>

                                <!-- Floors -->
                                <a href="{% url 'floor_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'floor' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-building mr-3 text-lg"></i>
                                    Floors
                                </a>
                            </div>
                        </div>

                        <!-- Reports Section -->
                        <div class="mt-6">
                            <h3 class="px-3 text-xs font-semibold text-gray-500 uppercase tracking-wider">Reports & Analytics</h3>
                            <div class="mt-2 space-y-1">
                                <!-- Reports -->
                                <a href="{% url 'reports_dashboard' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'report' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                                    <i class="fas fa-chart-bar mr-3 text-lg"></i>
                                    Reports Dashboard
                                </a>
                            </div>
                        </div>
                        {% endif %}

                        <!-- Search -->
                        <a href="{% url 'advanced_search' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'search' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                            <i class="fas fa-search mr-3 text-lg"></i>
                            Advanced Search
                        </a>

                        {% if user.can_manage_users %}
                        <!-- Users -->
                        <a href="{% url 'user_list' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'user' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                            <i class="fas fa-users mr-3 text-lg"></i>
                            User Management
                        </a>

                        <!-- Settings -->
                        <a href="{% url 'library_settings' %}" class="sidebar-item group flex items-center px-2 py-2 text-sm font-medium rounded-md {% if 'settings' in request.resolver_match.url_name %}bg-blue-100 text-blue-700 border-r-2 border-blue-700{% else %}text-gray-600 hover:bg-gray-50 hover:text-gray-900{% endif %}">
                            <i class="fas fa-cog mr-3 text-lg"></i>
                            Library Settings
                        </a>
                        {% endif %}

                        <!-- Quick Actions -->
                        {% if user.can_manage_books %}
                        <div class="pt-4 mt-4 border-t border-gray-200">
                            <p class="px-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">Quick Actions</p>
                            <div class="mt-2 space-y-1">
                                <a href="{% url 'book_add' %}" class="group flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-green-50 hover:text-green-700">
                                    <i class="fas fa-plus mr-3 text-green-600"></i>
                                    Add Book
                                </a>
                                <a href="{% url 'loan_create' %}" class="group flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-orange-50 hover:text-orange-700">
                                    <i class="fas fa-hand-holding mr-3 text-orange-600"></i>
                                    Issue Loan
                                </a>
                                <a href="{% url 'pending_requests' %}" class="group flex items-center px-2 py-2 text-sm font-medium text-gray-600 rounded-md hover:bg-blue-50 hover:text-blue-700">
                                    <i class="fas fa-clock mr-3 text-blue-600"></i>
                                    Review Requests
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </nav>

                    <!-- User Info at Bottom -->
                    <div class="flex-shrink-0 flex border-t border-gray-200 p-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                {% if user.profile_picture %}
                                <img src="{{ user.profile_picture.url }}" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                                {% else %}
                                <i class="fas fa-user text-gray-600 text-sm"></i>
                                {% endif %}
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-gray-700">{{ user.get_full_name|default:user.username }}</p>
                                <p class="text-xs text-gray-500">{{ user.get_role_display }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main content -->
        <div class="flex flex-col w-0 flex-1 overflow-hidden">
            <!-- Top navigation -->
            <div class="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
                <button class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-indigo-500 lg:hidden" onclick="toggleSidebar()">
                    <i class="fas fa-bars text-lg"></i>
                </button>
                <div class="flex-1 px-4 flex justify-between">
                    <div class="flex-1 flex">
                        <div class="w-full flex md:ml-0">
                            <form action="{% url 'advanced_search' %}" method="get" class="relative w-full text-gray-400 focus-within:text-gray-600">
                                <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-lg"></i>
                                </div>
                                <input id="search-field"
                                       name="query"
                                       class="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent"
                                       placeholder="Search books, users, loans..."
                                       type="search">
                            </form>
                        </div>
                    </div>
                    <div class="ml-4 flex items-center md:ml-6">
                        <!-- Notifications -->
                        <div class="relative">
                            <button id="notifications-btn" class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 relative">
                                <i class="fas fa-bell text-lg"></i>
                                <span id="notification-badge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center hidden">0</span>
                            </button>

                            <!-- Notifications Dropdown -->
                            <div id="notifications-dropdown" class="hidden absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                                <div class="p-4 border-b border-gray-200">
                                    <div class="flex items-center justify-between">
                                        <h3 class="text-lg font-semibold text-gray-900">Notifications</h3>
                                        <button id="mark-all-read-btn" class="text-sm text-blue-600 hover:text-blue-800">Mark all read</button>
                                    </div>
                                </div>
                                <div id="notification-list" class="max-h-96 overflow-y-auto">
                                    <!-- Notifications will be loaded here -->
                                </div>
                                <div class="p-4 border-t border-gray-200">
                                    <a href="{% url 'notifications_list' %}" class="text-sm text-blue-600 hover:text-blue-800">View all notifications</a>
                                </div>
                            </div>
                        </div>

                        <!-- Profile dropdown -->
                        <div class="ml-3 relative">
                            <div class="relative group">
                                <button class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                    <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                        {% if user.profile_picture %}
                                        <img src="{{ user.profile_picture.url }}" alt="Profile" class="w-8 h-8 rounded-full object-cover">
                                        {% else %}
                                        <i class="fas fa-user text-gray-600 text-sm"></i>
                                        {% endif %}
                                    </div>
                                </button>

                                <!-- Dropdown menu -->
                                <div class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                    <a href="{% url 'profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</a>
                                    <a href="{% url 'logout' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Sign out</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <main class="flex-1 relative overflow-y-auto focus:outline-none">
                <div class="py-6">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                        {% block content %}{% endblock %}
                    </div>
                </div>
            </main>
        </div>
    </div>
    {% else %}
    <!-- Non-authenticated layout -->
    <main class="flex-1">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            {% block content_unauthenticated %}{% endblock %}
        </div>
    </main>
    {% endif %}

    <!-- Footer -->
    <footer class="bg-gradient-to-r from-slate-800 to-slate-900 text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Library Info -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <i class="fas fa-book-open text-2xl text-blue-400"></i>
                        <h3 class="text-xl font-bold">LibraryPro</h3>
                    </div>
                    <p class="text-gray-300 mb-4">
                        A comprehensive library management system designed for educational institutions.
                        Manage books, users, loans, and reservations with ease.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors duration-200">
                            <i class="fab fa-facebook text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors duration-200">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors duration-200">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="{% url 'home' %}" class="text-gray-300 hover:text-white transition-colors duration-200">Dashboard</a></li>
                        <li><a href="{% url 'book_list' %}" class="text-gray-300 hover:text-white transition-colors duration-200">Browse Books</a></li>
                        {% if user.is_authenticated %}
                        <li><a href="{% url 'profile' %}" class="text-gray-300 hover:text-white transition-colors duration-200">My Profile</a></li>
                        {% endif %}
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact</h4>
                    <ul class="space-y-2 text-gray-300">
                        <li class="flex items-center">
                            <i class="fas fa-envelope mr-2 text-blue-400"></i>
                            <EMAIL>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone mr-2 text-blue-400"></i>
                            +****************
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-blue-400"></i>
                            123 Education St, Learning City
                        </li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-400">
                    &copy; 2024 LibraryPro Management System. All rights reserved.
                    <span class="text-blue-400">Built with ❤️ for education</span>
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        function toggleMobileMenu() {
            const menu = document.getElementById('mobileMenu');
            menu.classList.toggle('hidden');
        }

        // Auto-hide messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const messages = document.querySelectorAll('[class*="animate-slide-in"]');
            messages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0';
                    message.style.transform = 'translateX(100%)';
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);
            });
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add loading states to buttons
        document.querySelectorAll('button[type="submit"], .btn-primary').forEach(button => {
            button.addEventListener('click', function() {
                if (this.type === 'submit') {
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                    this.disabled = true;

                    // Re-enable after 3 seconds (fallback)
                    setTimeout(() => {
                        this.innerHTML = originalText;
                        this.disabled = false;
                    }, 3000);
                }
            });
        });

        // Notification System
        function loadNotifications() {
            fetch('/api/notifications/')
                .then(response => {
                    if (response.status === 401) {
                        // User not authenticated, don't show error
                        updateNotificationUI([], 0);
                        return;
                    }
                    return response.json();
                })
                .then(data => {
                    if (data) {
                        updateNotificationUI(data.notifications, data.unread_count);
                    }
                })
                .catch(error => {
                    console.error('Error loading notifications:', error);
                    updateNotificationUI([], 0);
                });
        }

        function updateNotificationUI(notifications, unreadCount) {
            const badge = document.getElementById('notification-badge');
            const notificationList = document.getElementById('notification-list');

            if (unreadCount > 0) {
                badge.textContent = unreadCount;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }

            if (notifications.length > 0) {
                notificationList.innerHTML = notifications.map(notification => `
                    <div class="px-4 py-3 hover:bg-gray-50 border-b border-gray-100 cursor-pointer ${!notification.is_read ? 'bg-blue-50' : ''}"
                         onclick="markNotificationRead(${notification.id})">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <div class="w-2 h-2 rounded-full ${getNotificationColor(notification.type)} mt-2"></div>
                            </div>
                            <div class="ml-3 flex-1">
                                <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                                <p class="text-sm text-gray-500">${notification.message}</p>
                                <p class="text-xs text-gray-400 mt-1">${notification.time_ago}</p>
                            </div>
                            ${!notification.is_read ? '<div class="w-2 h-2 bg-blue-500 rounded-full"></div>' : ''}
                        </div>
                    </div>
                `).join('');
            } else {
                notificationList.innerHTML = '<div class="px-4 py-3 text-sm text-gray-500 text-center">No notifications</div>';
            }
        }

        function getNotificationColor(type) {
            switch(type) {
                case 'book_due': return 'bg-yellow-400';
                case 'book_overdue': return 'bg-red-400';
                case 'book_returned': return 'bg-green-400';
                case 'request_approved': return 'bg-green-400';
                case 'request_rejected': return 'bg-red-400';
                default: return 'bg-blue-400';
            }
        }

        function markNotificationRead(notificationId) {
            fetch(`/api/notifications/${notificationId}/read/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications(); // Refresh notifications
                }
            })
            .catch(error => {
                console.error('Error marking notification as read:', error);
            });
        }

        function markAllNotificationsRead() {
            fetch('/api/notifications/mark-all-read/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadNotifications(); // Refresh notifications
                }
            })
            .catch(error => {
                console.error('Error marking all notifications as read:', error);
            });
        }

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Toggle notifications dropdown
        function toggleNotifications() {
            const dropdown = document.getElementById('notifications-dropdown');
            dropdown.classList.toggle('hidden');
            if (!dropdown.classList.contains('hidden')) {
                loadNotifications(); // Refresh when opening
            }
        }

        // Load notifications on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadNotifications();

            // Set up event listeners
            document.getElementById('notifications-btn').addEventListener('click', toggleNotifications);
            document.getElementById('mark-all-read-btn').addEventListener('click', markAllNotificationsRead);

            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                const dropdown = document.getElementById('notifications-dropdown');
                const button = document.getElementById('notifications-btn');
                if (!dropdown.contains(event.target) && !button.contains(event.target)) {
                    dropdown.classList.add('hidden');
                }
            });

            // Refresh notifications every 2 minutes
            setInterval(loadNotifications, 120000);
        });
    </script>

    <!-- Custom JavaScript -->
    <script src="{% static 'js/library.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
