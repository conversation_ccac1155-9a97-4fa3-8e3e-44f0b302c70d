#!/usr/bin/env python
"""
Test form submission functionality
"""

import os
import sys
import django
from django.test import Client
from django.contrib.auth import authenticate

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'library_management.settings')
django.setup()

from library.models import User, Book, Author, Category, Publisher, <PERSON>an

def test_user_creation_form():
    """Test user creation form submission"""
    print("Testing User Creation Form Submission...")
    
    client = Client()
    
    # Login as admin
    login_response = client.post('/login/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if login_response.status_code in [200, 302]:
        print("✅ Admin login successful")
        
        # Test user creation form
        user_data = {
            'username': 'testuser456',
            'email': '<EMAIL>',
            'first_name': 'Test',
            'last_name': 'User',
            'password1': 'testpass123',
            'password2': 'testpass123',
            'role': 'student',
            'enrollment_number': 'STU456'
        }
        
        response = client.post('/register/', user_data)
        print(f"User creation response status: {response.status_code}")
        
        if response.status_code == 302:  # Redirect after successful creation
            print("✅ User creation form submission successful")
            
            # Check if user was created
            try:
                created_user = User.objects.get(username='testuser456')
                print(f"✅ User created in database: {created_user.username}")
                
                # Clean up
                created_user.delete()
                print("✅ Test user cleaned up")
            except User.DoesNotExist:
                print("❌ User was not created in database")
        else:
            print(f"❌ User creation failed with status {response.status_code}")
            if hasattr(response, 'content'):
                print(f"Response content: {response.content[:500]}")
    else:
        print(f"❌ Admin login failed: {login_response.status_code}")

def test_book_creation_form():
    """Test book creation form submission"""
    print("\nTesting Book Creation Form Submission...")
    
    client = Client()
    
    # Login as admin
    login_response = client.post('/login/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if login_response.status_code in [200, 302]:
        print("✅ Admin login successful")
        
        # Get authors and categories
        authors = Author.objects.all()[:2]
        categories = Category.objects.all()[:2]
        
        if authors and categories:
            book_data = {
                'title': 'Test Book 123',
                'authors': [str(a.id) for a in authors],
                'categories': [str(c.id) for c in categories],
                'total_copies': 3,
                'isbn': 'TEST-BOOK-123',
                'language': 'English'
            }
            
            response = client.post('/books/add/', book_data)
            print(f"Book creation response status: {response.status_code}")
            
            if response.status_code == 302:  # Redirect after successful creation
                print("✅ Book creation form submission successful")
                
                # Check if book was created
                try:
                    created_book = Book.objects.get(title='Test Book 123')
                    print(f"✅ Book created in database: {created_book.title}")
                    
                    # Clean up
                    created_book.delete()
                    print("✅ Test book cleaned up")
                except Book.DoesNotExist:
                    print("❌ Book was not created in database")
            else:
                print(f"❌ Book creation failed with status {response.status_code}")
                if hasattr(response, 'content'):
                    print(f"Response content: {response.content[:500]}")
        else:
            print("❌ No authors or categories available for testing")
    else:
        print(f"❌ Admin login failed: {login_response.status_code}")

def test_loan_creation_form():
    """Test loan creation form submission"""
    print("\nTesting Loan Creation Form Submission...")
    
    client = Client()
    
    # Login as admin
    login_response = client.post('/login/', {
        'username': 'admin',
        'password': 'admin123'
    })
    
    if login_response.status_code in [200, 302]:
        print("✅ Admin login successful")
        
        # Get available books and borrowers
        available_books = Book.objects.filter(available_copies__gt=0)[:1]
        borrowers = User.objects.filter(role__in=['student', 'teacher'])[:1]
        
        if available_books and borrowers:
            book = available_books[0]
            borrower = borrowers[0]
            
            from datetime import datetime, timedelta
            due_date = datetime.now() + timedelta(days=14)
            
            loan_data = {
                'book': str(book.id),
                'borrower': str(borrower.id),
                'due_date': due_date.strftime('%Y-%m-%dT%H:%M'),
                'notes': 'Test loan submission'
            }
            
            response = client.post('/loans/create/', loan_data)
            print(f"Loan creation response status: {response.status_code}")
            
            if response.status_code == 302:  # Redirect after successful creation
                print("✅ Loan creation form submission successful")
                
                # Check if loan was created
                try:
                    created_loan = Loan.objects.filter(
                        book=book,
                        borrower=borrower,
                        notes='Test loan submission'
                    ).first()
                    
                    if created_loan:
                        print(f"✅ Loan created in database: {created_loan.id}")
                        
                        # Clean up
                        created_loan.delete()
                        # Restore book availability
                        book.available_copies += 1
                        book.save()
                        print("✅ Test loan cleaned up")
                    else:
                        print("❌ Loan was not created in database")
                except Exception as e:
                    print(f"❌ Error checking loan creation: {e}")
            else:
                print(f"❌ Loan creation failed with status {response.status_code}")
                if hasattr(response, 'content'):
                    print(f"Response content: {response.content[:500]}")
        else:
            print("❌ No available books or borrowers for testing")
    else:
        print(f"❌ Admin login failed: {login_response.status_code}")

def test_book_request_form():
    """Test book request form submission"""
    print("\nTesting Book Request Form Submission...")
    
    client = Client()
    
    # Login as student
    student_users = User.objects.filter(role='student')[:1]
    if not student_users:
        print("❌ No student users available for testing")
        return
    
    student = student_users[0]
    
    # Try to login as student (assuming password is username + '123')
    login_response = client.post('/login/', {
        'username': student.username,
        'password': student.username + '123'
    })
    
    if login_response.status_code not in [200, 302]:
        # Try with admin
        login_response = client.post('/login/', {
            'username': 'admin',
            'password': 'admin123'
        })
    
    if login_response.status_code in [200, 302]:
        print("✅ User login successful")
        
        # Get available books
        available_books = Book.objects.filter(available_copies__gt=0)[:1]
        
        if available_books:
            book = available_books[0]
            
            response = client.post(f'/request-book/{book.id}/', {})
            print(f"Book request response status: {response.status_code}")
            
            if response.status_code == 302:  # Redirect after successful request
                print("✅ Book request form submission successful")
                
                # Check if loan request was created
                try:
                    loan_request = Loan.objects.filter(
                        book=book,
                        status='pending'
                    ).first()
                    
                    if loan_request:
                        print(f"✅ Book request created in database: {loan_request.id}")
                        
                        # Clean up
                        loan_request.delete()
                        print("✅ Test book request cleaned up")
                    else:
                        print("❌ Book request was not created in database")
                except Exception as e:
                    print(f"❌ Error checking book request: {e}")
            else:
                print(f"❌ Book request failed with status {response.status_code}")
                if hasattr(response, 'content'):
                    print(f"Response content: {response.content[:500]}")
        else:
            print("❌ No available books for testing")
    else:
        print(f"❌ User login failed: {login_response.status_code}")

def main():
    """Run all form submission tests"""
    print("🔍 Testing Form Submission Functionality")
    print("=" * 50)
    
    test_user_creation_form()
    test_book_creation_form()
    test_loan_creation_form()
    test_book_request_form()
    
    print("\n" + "=" * 50)
    print("✅ Form submission tests completed!")

if __name__ == '__main__':
    main()
